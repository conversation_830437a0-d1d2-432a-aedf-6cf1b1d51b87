use std::{io::stdout, time::Duration};

use anyhow::Result;
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ratatui::{
    Terminal,
    backend::CrosstermBackend,
    layout::{Constraint, Direction, Layout},
    style::{Modifier, Style},
    widgets::{Block, Borders, List, ListItem, Paragraph, Wrap},
};

use sichuan_mahjong::game::tile::Tile;
use sichuan_mahjong::network::{GameMessage, NetworkManager};

struct App {
    room_code: String,
    player_name: String,
    player_id: String,
    network: NetworkManager,
    state: AppState,
    tiles: Vec<Tile>,
}

enum AppState {
    Menu,
    RoomInput,
    Game {
        room_id: String,
        players: Vec<(String, String)>, // (id, name)
        is_ready: bool,
    },
    Error(String),
}

impl App {
    async fn new() -> Result<Self> {
        let addr = "127.0.0.1:6666".to_string();
        let mut network = NetworkManager::new(addr).await?;
        network.connect().await?;

        let player_id = format!("player_{}", uuid::Uuid::new_v4());

        Ok(Self {
            room_code: String::new(),
            player_name: "玩家".to_string(),
            player_id,
            network,
            state: AppState::Menu,
            tiles: Vec::new(),
        })
    }
}

async fn run_app(
    terminal: &mut Terminal<CrosstermBackend<std::io::Stdout>>,
    app: &mut App,
) -> Result<()> {
    let network = &mut app.network;

    loop {
        terminal.draw(|frame| {
            let size = frame.size();
            match &app.state {
                AppState::Menu => {
                    let menu_items = vec!["创建房间 [C]", "加入房间 [J]", "退出游戏 [Q]"]
                        .into_iter()
                        .map(ListItem::new)
                        .collect::<Vec<_>>();

                    let menu = List::new(menu_items)
                        .block(Block::default().title("主菜单").borders(Borders::ALL))
                        .highlight_style(Style::default().add_modifier(Modifier::REVERSED));
                    frame.render_widget(menu, size);
                }
                AppState::RoomInput => {
                    let input = Paragraph::new(app.room_code.clone())
                        .block(Block::default().title("输入房间号").borders(Borders::ALL))
                        .wrap(Wrap { trim: true });
                    frame.render_widget(input, size);
                }
                AppState::Game {
                    room_id,
                    players,
                    is_ready,
                } => {
                    let chunks = Layout::default()
                        .direction(Direction::Vertical)
                        .margin(1)
                        .constraints(
                            [
                                Constraint::Length(3),
                                Constraint::Length(3),
                                Constraint::Min(0),
                            ]
                            .as_ref(),
                        )
                        .split(size);

                    // 显示房间信息
                    let room_info = format!("房间号: {}", room_id);
                    let room_widget = Paragraph::new(room_info)
                        .block(Block::default().title("房间信息").borders(Borders::ALL))
                        .wrap(Wrap { trim: true });
                    frame.render_widget(room_widget, chunks[0]);

                    // 显示玩家列表
                    let player_list: Vec<ListItem> = players
                        .iter()
                        .map(|(id, name)| {
                            let text = if id == &app.player_id {
                                format!("{} (你){}", name, if !is_ready { " (未准备)" } else { "" })
                            } else {
                                name.clone()
                            };
                            ListItem::new(text)
                        })
                        .collect();
                    let players_widget = List::new(player_list)
                        .block(Block::default().title("玩家列表").borders(Borders::ALL))
                        .highlight_style(Style::default().add_modifier(Modifier::REVERSED));
                    frame.render_widget(players_widget, chunks[1]);

                    // 显示游戏区域
                    let game_area = if app.tiles.is_empty() {
                        "等待游戏开始..."
                    } else {
                        "游戏进行中..."
                    };
                    let game_widget = Paragraph::new(game_area)
                        .block(Block::default().title("游戏").borders(Borders::ALL))
                        .wrap(Wrap { trim: true });
                    frame.render_widget(game_widget, chunks[2]);
                }
                AppState::Error(msg) => {
                    let error = Paragraph::new(msg.clone())
                        .block(Block::default().title("错误").borders(Borders::ALL))
                        .wrap(Wrap { trim: true });
                    frame.render_widget(error, size);
                }
            }
        })?;

        if event::poll(Duration::from_millis(100))?
            && let Event::Key(key) = event::read()?
            && key.kind == KeyEventKind::Press
        {
            match &app.state {
                AppState::Menu => match key.code {
                    KeyCode::Char('c') | KeyCode::Char('C') => {
                        network
                            .send_message(GameMessage::CreateRoom {
                                player_id: app.player_id.clone(),
                                name: app.player_name.clone(),
                            })
                            .await?;
                    }
                    KeyCode::Char('j') | KeyCode::Char('J') => {
                        app.state = AppState::RoomInput;
                    }
                    KeyCode::Char('q') | KeyCode::Char('Q') => {
                        return Ok(());
                    }
                    _ => {}
                },
                AppState::RoomInput => match key.code {
                    KeyCode::Enter => {
                        if !app.room_code.is_empty() {
                            network
                                .send_message(GameMessage::JoinRoom {
                                    player_id: app.player_id.clone(),
                                    name: app.player_name.clone(),
                                    room_id: app.room_code.clone(),
                                })
                                .await?;
                        }
                    }
                    KeyCode::Char(c) => {
                        app.room_code.push(c);
                    }
                    KeyCode::Backspace => {
                        app.room_code.pop();
                    }
                    KeyCode::Esc => {
                        app.state = AppState::Menu;
                        app.room_code.clear();
                    }
                    _ => {}
                },
                AppState::Game {
                    room_id,
                    players: _,
                    is_ready,
                } => match key.code {
                    KeyCode::Char(' ') if !is_ready => {
                        network
                            .send_message(GameMessage::Ready {
                                player_id: app.player_id.clone(),
                            })
                            .await?;
                    }
                    KeyCode::Esc => {
                        network
                            .send_message(GameMessage::LeaveRoom {
                                player_id: app.player_id.clone(),
                                room_id: room_id.clone(),
                            })
                            .await?;
                        app.state = AppState::Menu;
                    }
                    _ => {}
                },
                AppState::Error(_) => match key.code {
                    KeyCode::Esc | KeyCode::Enter => {
                        app.state = AppState::Menu;
                    }
                    _ => {}
                },
            }
        }

        // 接收并处理服务器消息
        if let Ok(msg) = network.try_recv_message().await {
            match msg {
                GameMessage::GameStart { room_id } => {
                    if let AppState::Game {
                        players, is_ready, ..
                    } = &app.state
                    {
                        app.state = AppState::Game {
                            room_id,
                            players: players.clone(),
                            is_ready: *is_ready,
                        };
                    }
                }
                GameMessage::PlayerList { players } => {
                    if let AppState::Game {
                        room_id, is_ready, ..
                    } = &app.state
                    {
                        app.state = AppState::Game {
                            room_id: room_id.clone(),
                            players,
                            is_ready: *is_ready,
                        };
                    }
                }
                GameMessage::Error { message } => {
                    app.state = AppState::Error(message);
                }
                _ => {}
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // 设置终端
    enable_raw_mode()?;
    let mut stdout = stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // 创建应用状态
    let mut app = App::new().await?;

    // 设置清理
    let result = run_app(&mut terminal, &mut app).await;

    // 恢复终端
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    if let Err(err) = result {
        println!("{err:?}");
    }

    Ok(())
}
