use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::broadcast::{self, Sender};
use tokio::sync::Mutex;
use uuid::Uuid;

use super::room::Room;
use super::GameMessage;

pub struct NetworkManager {
    addr: String,
    rooms: Arc<Mutex<HashMap<String, Room>>>,
    message_tx: Option<Sender<GameMessage>>,
    stream: Option<Arc<tokio::sync::Mutex<TcpStream>>>,
}

impl NetworkManager {
    pub async fn new(addr: String) -> Result<Self> {
        Ok(Self {
            addr,
            rooms: Arc::new(Mutex::new(HashMap::new())),
            message_tx: None,
            stream: None,
        })
    }

    // 连接到服务器
    pub async fn connect(&mut self) -> Result<()> {
        let stream = TcpStream::connect(&self.addr).await?;
        let (tx, _) = broadcast::channel(100);
        self.message_tx = Some(tx.clone());

        // 使用 Arc 和 Mutex 包装流，以便在不同任务之间共享
        let stream = Arc::new(tokio::sync::Mutex::new(stream));
        self.stream = Some(stream.clone());

        let tx = tx.clone();
        let stream = stream.clone();
        tokio::spawn(async move {
            let mut buffer = [0u8; 1024];
            loop {
                let mut stream = stream.lock().await;
                if let Ok(n) = stream.read(&mut buffer).await {
                    if n == 0 {
                        break;
                    }
                    if let Ok(msg) = String::from_utf8(buffer[..n].to_vec()) {
                        match serde_json::from_str::<GameMessage>(&msg) {
                            Ok(game_msg) => {
                                if let Err(e) = tx.send(game_msg) {
                                    eprintln!("Error broadcasting message: {}", e);
                                }
                            }
                            Err(e) => eprintln!("Error parsing message: {}", e),
                        }
                    }
                }
            }
        });
        Ok(())
    }

    // 作为服务器启动
    pub async fn start_server(&mut self) -> Result<()> {
        let listener = TcpListener::bind(&self.addr).await?;
        println!("Server started on {}", self.addr);

        let (tx, _) = broadcast::channel(100);
        self.message_tx = Some(tx.clone());

        while let Ok((stream, addr)) = listener.accept().await {
            println!("New connection from: {}", addr);
            let rooms = Arc::clone(&self.rooms);

            tokio::spawn(async move {
                if let Err(e) = Self::handle_connection(stream, rooms).await {
                    eprintln!("Error handling connection: {}", e);
                }
            });
        }

        Ok(())
    }

    // 发送消息
    pub async fn send_message(&self, message: GameMessage) -> Result<()> {
        if let Some(tx) = &self.message_tx {
            // 先序列化消息，因为发送到 channel 会消耗所有权
            let msg = serde_json::to_string(&message)?;

            tx.send(message)?;

            if let Some(stream) = &self.stream {
                let mut stream = stream.lock().await;
                stream.write_all(msg.as_bytes()).await?;
                stream.write_all(b"\n").await?;
            }
            Ok(())
        } else {
            Err(anyhow::anyhow!("Not connected"))
        }
    }

    // 接收消息
    pub async fn try_recv_message(&self) -> Result<GameMessage> {
        if let Some(tx) = &self.message_tx {
            let mut rx = tx.subscribe();
            match rx.try_recv() {
                Ok(msg) => Ok(msg),
                Err(_) => Err(anyhow::anyhow!("No message available")),
            }
        } else {
            Err(anyhow::anyhow!("Not connected"))
        }
    }

    // 处理单个连接
    async fn handle_connection(
        mut stream: TcpStream,
        rooms: Arc<Mutex<HashMap<String, Room>>>,
    ) -> Result<()> {
        let mut buffer = [0u8; 1024];

        loop {
            match stream.readable().await {
                Ok(_) => match stream.try_read(&mut buffer) {
                    Ok(n) if n > 0 => {
                        if let Ok(msg) = String::from_utf8(buffer[..n].to_vec()) {
                            match serde_json::from_str::<GameMessage>(&msg) {
                                Ok(game_msg) => match game_msg {
                                    GameMessage::CreateRoom { player_id, name } => {
                                        let room_id = format!("room_{}", Uuid::new_v4());
                                        let mut room = Room::new(room_id.clone());
                                        room.add_player(player_id.clone(), name);

                                        let mut rooms = rooms.lock().await;
                                        rooms.insert(room.id.clone(), room.clone());

                                        let response = GameMessage::RoomList {
                                            rooms: vec![(room.id, room.players.len() as i32)],
                                        };
                                        let msg = serde_json::to_string(&response)?;
                                        stream.write_all(msg.as_bytes()).await?;
                                        stream.write_all(b"\n").await?;
                                    }
                                    GameMessage::JoinRoom {
                                        player_id,
                                        name,
                                        room_id,
                                    } => {
                                        let mut rooms = rooms.lock().await;
                                        if let Some(room) = rooms.get_mut(&room_id) {
                                            if room.is_full() {
                                                let error_msg = GameMessage::Error {
                                                    message: "Room is full".to_string(),
                                                };
                                                let msg = serde_json::to_string(&error_msg)?;
                                                stream.write_all(msg.as_bytes()).await?;
                                                stream.write_all(b"\n").await?;
                                            } else if room.add_player(player_id, name) {
                                                let response = GameMessage::PlayerList {
                                                    players: room.players.clone(),
                                                };
                                                let msg = serde_json::to_string(&response)?;
                                                stream.write_all(msg.as_bytes()).await?;
                                                stream.write_all(b"\n").await?;
                                            } else {
                                                let error_msg = GameMessage::Error {
                                                    message: "Failed to join room".to_string(),
                                                };
                                                let msg = serde_json::to_string(&error_msg)?;
                                                stream.write_all(msg.as_bytes()).await?;
                                                stream.write_all(b"\n").await?;
                                            }
                                        } else {
                                            let error_msg = GameMessage::Error {
                                                message: "Room not found".to_string(),
                                            };
                                            let msg = serde_json::to_string(&error_msg)?;
                                            stream.write_all(msg.as_bytes()).await?;
                                            stream.write_all(b"\n").await?;
                                        }
                                    }
                                    _ => {}
                                },
                                Err(e) => eprintln!("Error parsing message: {}", e),
                            }
                        }
                    }
                    Ok(_) => break,
                    Err(ref e) if e.kind() == std::io::ErrorKind::WouldBlock => {
                        continue;
                    }
                    Err(e) => {
                        eprintln!("Error reading from stream: {}", e);
                        break;
                    }
                },
                Err(e) => {
                    eprintln!("Error waiting for readable: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }
}
