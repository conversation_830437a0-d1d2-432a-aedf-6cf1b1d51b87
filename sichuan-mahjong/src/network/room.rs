use crate::game::state::GameState;
use std::sync::Arc;
use tokio::sync::Mutex;

#[derive(Debug, Clone)]
pub struct Room {
    pub id: String,
    pub players: Vec<(String, String)>, // (player_id, name)
    #[allow(dead_code)]
    pub game_state: Option<Arc<Mutex<GameState>>>,
}

impl Room {
    pub fn new(id: String) -> Self {
        Self {
            id,
            players: Vec::new(),
            game_state: None,
        }
    }

    pub fn add_player(&mut self, player_id: String, name: String) -> bool {
        if self.players.len() < 4 && !self.has_player(&player_id) {
            self.players.push((player_id, name));
            true
        } else {
            false
        }
    }

    #[allow(dead_code)]
    pub fn remove_player(&mut self, player_id: &str) {
        self.players.retain(|(id, _)| id != player_id);
    }

    pub fn has_player(&self, player_id: &str) -> bool {
        self.players.iter().any(|(id, _)| id == player_id)
    }

    pub fn is_full(&self) -> bool {
        self.players.len() >= 4
    }

    #[allow(dead_code)]
    pub fn is_empty(&self) -> bool {
        self.players.is_empty()
    }
}
