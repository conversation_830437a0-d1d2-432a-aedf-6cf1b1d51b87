use crate::game::rules::GameAction;
use crate::game::tile::Tile;
use serde::{Deserialize, Serialize};

mod manager;
mod room;

pub use manager::NetworkManager;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum GameMessage {
    // 房间管理
    CreateRoom {
        player_id: String,
        name: String,
    },
    JoinRoom {
        player_id: String,
        name: String,
        room_id: String,
    },
    LeaveRoom {
        player_id: String,
        room_id: String,
    },
    Ready {
        player_id: String,
    },

    // 游戏操作
    DrawTile {
        player_id: String,
    },
    DiscardTile {
        player_id: String,
        tile: Tile,
    },
    Action {
        player_id: String,
        action: GameAction,
    },

    // 游戏状态
    GameStart {
        room_id: String,
    },
    PlayerTurn {
        player_id: String,
    },
    TileDiscarded {
        player_id: String,
        tile: Tile,
    },
    ActionRequest {
        player_id: String,
        possible_actions: Vec<GameAction>,
    },

    // 通知消息
    Error {
        message: String,
    },
    PlayerList {
        players: Vec<(String, String)>,
    }, // (id, name)
    RoomList {
        rooms: Vec<(String, i32)>,
    }, // (room_id, player_count)
}
