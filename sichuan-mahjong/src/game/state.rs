use crate::game::player::Player;
use crate::game::tile::{Tile, TileType};
use rand::prelude::*;
use std::collections::HashMap;

#[allow(dead_code)]
#[derive(Debug)]
pub struct GameState {
    pub players: HashMap<String, Player>,
    pub wall: Vec<Tile>,
    pub current_player: Option<String>,
    pub game_started: bool,
}

// 为 GameState 实现 Default trait，这是一种更惯用的方式来创建默认实例。
impl Default for GameState {
    /// 创建一个表示新游戏的默认 GameState。
    ///
    /// # 增强功能说明
    ///
    /// 1.  **最佳实践与模式**:
    ///     - 实现了 `Default` trait，取代了自定义的 `new` 函数。
    ///     - 这是 Rust 中为类型提供默认值的惯用方式，提高了与其他期望 `Default` 实例的 API 的互操作性。
    ///
    /// 2.  **性能优化**:
    ///     - `HashMap::with_capacity(4)`: 为玩家预分配了容量。由于麻将游戏通常有4名玩家，这可以避免在添加玩家时进行多次重新分配和哈希重计算。
    ///     - `Vec::with_capacity(108)`: 为牌墙预分配了容量。一副不含字牌和花牌的麻将有108张牌（万、筒、条各36张）。这避免了在 `initialize_wall` 过程中 `Vec` 的动态增长，从而提高了效率。
    ///
    /// 3.  **代码可读性与可维护性**:
    ///     - 使用 `Default::default()` 或 `GameState::default()` 来创建新实例，意图清晰明确。
    ///     - 将“魔数”（如4和108）的意图通过 `with_capacity` 调用和注释清晰地表达出来，使代码更易于理解和维护。
    ///
    /// 4.  **错误处理与边界情况**:
    ///     - 这个特定的构造函数本身不处理运行时错误，但通过预分配容量，它为后续操作（如添加玩家和初始化牌墙）的性能和稳定性奠定了基础，减少了因内存重新分配可能导致的潜在性能问题。
    fn default() -> Self {
        const MAX_PLAYERS: usize = 4;
        const MAX_TILES: usize = 108; // 3 types * 9 numbers * 4 copies

        Self {
            players: HashMap::with_capacity(MAX_PLAYERS),
            wall: Vec::with_capacity(MAX_TILES),
            current_player: None,
            game_started: false,
        }
    }
}

impl GameState {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn initialize_wall(&mut self) {
        self.wall.clear();

        // 生成所有牌（万、筒、条，每种牌4张）
        for i in 1..=9 {
            for _ in 0..4 {
                self.wall.push(Tile::new(TileType::Character(i)));
                self.wall.push(Tile::new(TileType::Circle(i)));
                self.wall.push(Tile::new(TileType::Bamboo(i)));
            }
        }

        // 洗牌
        let mut rng = rand::rngs::ThreadRng::default();
        self.wall.shuffle(&mut rng);
    }

    pub fn add_player(&mut self, id: String, name: String) -> bool {
        if self.players.len() >= 4 || self.game_started {
            return false;
        }
        self.players.insert(id.clone(), Player::new(id, name));
        true
    }

    pub fn start_game(&mut self) -> bool {
        if self.players.len() != 4 || self.game_started {
            return false;
        }

        self.initialize_wall();
        self.game_started = true;

        // 发牌
        for _ in 0..13 {
            for player in self.players.values_mut() {
                if let Some(tile) = self.wall.pop() {
                    player.draw_tile(tile);
                }
            }
        }

        // 随机选择起始玩家
        let player_ids: Vec<String> = self.players.keys().cloned().collect();
        let mut rng = rand::rngs::ThreadRng::default();
        self.current_player = player_ids.choose(&mut rng).cloned();

        true
    }

    #[allow(dead_code)]
    pub fn draw_tile(&mut self, player_id: &str) -> Option<Tile> {
        if !self.game_started || self.current_player.as_deref() != Some(player_id) {
            return None;
        }
        self.wall.pop()
    }

    #[allow(dead_code)]
    pub fn next_player(&mut self) {
        if let Some(current_id) = &self.current_player {
            let player_ids: Vec<String> = self.players.keys().cloned().collect();
            let current_index = player_ids.iter().position(|id| id == current_id).unwrap();
            let next_index = (current_index + 1) % player_ids.len();
            self.current_player = Some(player_ids[next_index].clone());
        }
    }
}
