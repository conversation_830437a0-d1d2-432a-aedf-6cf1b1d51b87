use crate::game::tile::{Tile, TileType};
use std::collections::HashMap;

// 胡牌类型
#[derive(Debu<PERSON>, Clone)]
pub enum WinPattern {
    PingHu,    // 平胡
    QiDui,     // 七对
    DaDui,     // 大对子（碰碰胡）
    QingYiSe,  // 清一色
    Qing<PERSON>eng,  // 清碰（清一色碰碰胡）
    QingQiDui, // 清一色七对
    LongQiDui, // 龙七对
}

// 统计牌的数量
fn count_tiles(tiles: &[Tile]) -> HashMap<Tile, u8> {
    tiles.iter().fold(HashMap::new(), |mut counter, &tile| {
        *counter.entry(tile).or_insert(0) += 1;
        counter
    })
}

// 检查是否是七对子
fn check_seven_pairs(counter: &HashMap<Tile, u8>) -> Option<WinPattern> {
    if counter.len() != 7 {
        return None;
    }

    let mut has_four = false;
    for &count in counter.values() {
        if count != 2 && count != 4 {
            return None;
        }
        if count == 4 {
            has_four = true;
        }
    }

    if has_four {
        Some(WinPattern::LongQiDui)
    } else {
        Some(WinPattern::QiDui)
    }
}

// 检查是否是清一色
fn is_one_suit(tiles: &[Tile]) -> bool {
    if tiles.is_empty() {
        return false;
    }

    let first_type = match tiles[0].tile_type {
        TileType::Character(_) => TileType::Character(1),
        TileType::Circle(_) => TileType::Circle(1),
        TileType::Bamboo(_) => TileType::Bamboo(1),
    };

    tiles.iter().all(|tile| {
        matches!(
            (tile.tile_type, first_type),
            (TileType::Character(_), TileType::Character(_))
                | (TileType::Circle(_), TileType::Circle(_))
                | (TileType::Bamboo(_), TileType::Bamboo(_))
        )
    })
}

// 检查是否是对对胡（碰碰胡）
fn check_all_triplets(counter: &HashMap<Tile, u8>) -> bool {
    let pair_count = counter.values().filter(|&&count| count == 2).count();
    let all_valid = counter.values().all(|&count| count == 2 || count == 3);

    pair_count == 1 && all_valid
}

// 检查顺子
fn find_sequence(tiles: &[Tile], mut needed: u8) -> bool {
    if tiles.len() < 3 {
        return false;
    }

    let mut i = 0;
    while i < tiles.len() - 2 && needed > 0 {
        match tiles[i].tile_type {
            TileType::Character(n) | TileType::Circle(n) | TileType::Bamboo(n) => {
                if n > 7 {
                    i += 1;
                    continue;
                }

                if let (Some(next), Some(next2)) = (tiles.get(i + 1), tiles.get(i + 2)) {
                    match (next.tile_type, next2.tile_type) {
                        (TileType::Character(n2), TileType::Character(n3))
                        | (TileType::Circle(n2), TileType::Circle(n3))
                        | (TileType::Bamboo(n2), TileType::Bamboo(n3)) => {
                            if n2 == n + 1 && n3 == n + 2 {
                                needed -= 1;
                                i += 3;
                                continue;
                            }
                        }
                        _ => {}
                    }
                }
            }
        }
        i += 1;
    }

    needed == 0
}

// 判断是否胡牌，返回胡牌类型
pub fn check_win(hand: &[Tile], melds: &[Tile]) -> Option<WinPattern> {
    let mut all_tiles = hand.to_vec();
    all_tiles.extend_from_slice(melds);

    // 首先检查特殊牌型
    let counter = count_tiles(&all_tiles);

    // 检查七对子
    if let Some(pattern) = check_seven_pairs(&counter) {
        if is_one_suit(&all_tiles) {
            return Some(WinPattern::QingQiDui);
        }
        return Some(pattern);
    }

    // 检查清一色碰碰胡
    if is_one_suit(&all_tiles) && check_all_triplets(&counter) {
        return Some(WinPattern::QingPeng);
    }

    // 检查大对子（碰碰胡）
    if check_all_triplets(&counter) {
        return Some(WinPattern::DaDui);
    }

    // 检查清一色
    if is_one_suit(&all_tiles) {
        return Some(WinPattern::QingYiSe);
    }

    // 检查平胡
    // 检查是否有一个对子和四个顺子/刻子组合
    for (tile, &count) in &counter {
        if count >= 2 {
            let mut remaining: Vec<_> = all_tiles
                .iter()
                .filter(|&t| {
                    t != tile || {
                        // 允许相同牌通过两次，其余的都过滤掉
                        static mut COUNT: u8 = 0;
                        unsafe {
                            if COUNT < 2 {
                                COUNT += 1;
                                false
                            } else {
                                true
                            }
                        }
                    }
                })
                .cloned()
                .collect();

            remaining.sort_by(|a, b| {
                use TileType::*;
                match (a.tile_type, b.tile_type) {
                    (Character(n1), Character(n2))
                    | (Circle(n1), Circle(n2))
                    | (Bamboo(n1), Bamboo(n2)) => n1.cmp(&n2),
                    (Character(_), _) => std::cmp::Ordering::Less,
                    (Circle(_), Character(_)) => std::cmp::Ordering::Greater,
                    (Circle(_), _) => std::cmp::Ordering::Less,
                    (Bamboo(_), _) => std::cmp::Ordering::Greater,
                }
            });

            // 检查剩余牌是否能组成4个顺子或刻子
            if check_regular_pattern(&remaining) {
                return Some(WinPattern::PingHu);
            }
        }
    }

    None
}

// 检查常规牌型（四个顺子或刻子）
fn check_regular_pattern(tiles: &[Tile]) -> bool {
    if tiles.len() != 12 {
        // 除了对子，还应该正好有12张牌
        return false;
    }

    let counter = count_tiles(tiles);
    let mut needed_sets = 4; // 需要找到4个顺子或刻子

    // 先检查刻子
    for &count in counter.values() {
        if count >= 3 {
            needed_sets -= 1;
        }
    }

    // 再检查顺子
    find_sequence(tiles, needed_sets)
}
