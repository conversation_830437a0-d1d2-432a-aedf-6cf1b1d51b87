use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, <PERSON>q, <PERSON>h, Serialize, Deserialize)]
pub enum TileType {
    Character(u8), // 万子
    Circle(u8),    // 筒子
    Bamboo(u8),    // 条子
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, <PERSON>h, Serialize, Deserialize)]
pub struct Tile {
    pub tile_type: TileType,
}

impl Tile {
    pub fn new(tile_type: TileType) -> Self {
        Self { tile_type }
    }
}

impl fmt::Display for Tile {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self.tile_type {
            TileType::Character(n) => write!(f, "{}万", n),
            TileType::Circle(n) => write!(f, "{}筒", n),
            TileType::Bamboo(n) => write!(f, "{}条", n),
        }
    }
}
