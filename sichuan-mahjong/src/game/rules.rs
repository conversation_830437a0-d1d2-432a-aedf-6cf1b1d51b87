use serde::{Deserialize, Serialize};
use crate::game::tile::Tile;

#[derive(Debug, Serialize, Deserialize)]
pub enum MeldType {
    Chi,    // 吃
    Peng,   // 碰
    Gang,   // 杠
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Meld {
    pub meld_type: MeldType,
    pub tiles: Vec<Tile>,
    pub source_player: Option<String>, // 来源玩家ID，自己的暗杠为None
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum WinType {
    SelfDraw,           // 自摸
    Rob,                // 抢杠胡
    Regular,            // 普通胡牌
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WinInfo {
    pub win_type: WinType,
    pub winner: String,        // 获胜玩家ID
    pub from: Option<String>,  // 点炮玩家ID（自摸时为None）
    pub score: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GameAction {
    Pass,
    Discard(Tile),
    Chi(Vec<Tile>),
    Peng(Vec<Tile>),
    <PERSON>(Vec<Tile>),
    Win(WinType),
}
