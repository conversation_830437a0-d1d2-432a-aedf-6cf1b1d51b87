[package]
name = "sichuan-mahjong"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "mahjong-server"
path = "src/bin/server.rs"

[[bin]]
name = "mahjong-client"
path = "src/bin/client.rs"

[lib]
path = "src/lib.rs"

[dependencies]
anyhow = "1.0.99"
rand = "0.9.2"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.143"
tokio = { version = "1.47.1", features = ["full"] }
uuid = { version = "1.18.1", features = ["v4"] }
crossterm = "0.27"
ratatui = { version = "0.23.0", features = ["crossterm"] }

[workspace]
members = [
  "."
]
